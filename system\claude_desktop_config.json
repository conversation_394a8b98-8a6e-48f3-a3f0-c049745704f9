{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop", "C:\\Users\\<USER>\\Downloads", "C:\\Users\\<USER>\\.augment"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "YOUR_BRAVE_API_KEY_HERE"}}, "legendary-ai-chat": {"command": "node", "args": ["C:\\Users\\<USER>\\.augment\\scripts\\legendary-ai-chat.js"]}, "legendary-design": {"command": "node", "args": ["C:\\Users\\<USER>\\.augment\\scripts\\legendary-design.js"]}, "legendary-image-gen": {"command": "node", "args": ["C:\\Users\\<USER>\\.augment\\scripts\\legendary-image-gen.js"]}, "legendary-search": {"command": "node", "args": ["C:\\Users\\<USER>\\.augment\\scripts\\legendary-search.js"]}, "legendary-video": {"command": "node", "args": ["C:\\Users\\<USER>\\.augment\\scripts\\legendary-video.js"]}}, "globalShortcut": "Ctrl+Shift+A", "theme": "dark", "autoStart": true, "enableLogging": true, "logLevel": "info"}