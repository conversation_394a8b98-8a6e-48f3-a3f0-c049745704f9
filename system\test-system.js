#!/usr/bin/env node

/**
 * Augment-Legendary System Test
 * Verifies all components are working correctly
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class SystemTest {
    constructor() {
        this.userHome = os.homedir();
        this.systemPath = path.join(this.userHome, '.augment');
        this.claudeConfigPath = path.join(process.env.APPDATA, 'Claude', 'claude_desktop_config.json');
        this.results = [];
    }

    async runTests() {
        console.log('🧪 Running Augment-Legendary System Tests...\n');

        this.testDirectoryStructure();
        this.testSystemFiles();
        this.testClaudeConfig();
        this.testMCPScripts();
        this.generateReport();
    }

    testDirectoryStructure() {
        console.log('📁 Testing directory structure...');
        
        const requiredDirs = [
            this.systemPath,
            path.join(this.systemPath, 'requests'),
            path.join(this.systemPath, 'responses'),
            path.join(this.systemPath, 'configs'),
            path.join(this.systemPath, 'scripts')
        ];

        requiredDirs.forEach(dir => {
            if (fs.existsSync(dir)) {
                console.log(`  ✅ ${dir}`);
                this.results.push({ test: `Directory: ${path.basename(dir)}`, status: 'PASS' });
            } else {
                console.log(`  ❌ ${dir}`);
                this.results.push({ test: `Directory: ${path.basename(dir)}`, status: 'FAIL' });
            }
        });
    }

    testSystemFiles() {
        console.log('\n📋 Testing system files...');
        
        const requiredFiles = [
            path.join(this.systemPath, 'augment-bridge.js'),
            path.join(this.systemPath, 'configs', 'claude_desktop_config.json'),
            path.join(this.systemPath, 'scripts', 'legendary-design.js')
        ];

        requiredFiles.forEach(file => {
            if (fs.existsSync(file)) {
                console.log(`  ✅ ${path.basename(file)}`);
                this.results.push({ test: `File: ${path.basename(file)}`, status: 'PASS' });
            } else {
                console.log(`  ❌ ${path.basename(file)}`);
                this.results.push({ test: `File: ${path.basename(file)}`, status: 'FAIL' });
            }
        });
    }

    testClaudeConfig() {
        console.log('\n⚙️ Testing Claude Desktop configuration...');
        
        if (fs.existsSync(this.claudeConfigPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(this.claudeConfigPath, 'utf8'));
                
                if (config.mcpServers) {
                    const serverCount = Object.keys(config.mcpServers).length;
                    console.log(`  ✅ Claude config exists with ${serverCount} MCP servers`);
                    this.results.push({ test: 'Claude Desktop Config', status: 'PASS' });
                    
                    // Test individual servers
                    Object.keys(config.mcpServers).forEach(serverName => {
                        console.log(`    📡 ${serverName}`);
                    });
                } else {
                    console.log(`  ❌ Claude config missing mcpServers section`);
                    this.results.push({ test: 'Claude Desktop Config', status: 'FAIL' });
                }
            } catch (error) {
                console.log(`  ❌ Claude config invalid JSON: ${error.message}`);
                this.results.push({ test: 'Claude Desktop Config', status: 'FAIL' });
            }
        } else {
            console.log(`  ❌ Claude config not found at: ${this.claudeConfigPath}`);
            this.results.push({ test: 'Claude Desktop Config', status: 'FAIL' });
        }
    }

    testMCPScripts() {
        console.log('\n🔧 Testing MCP scripts...');
        
        const mcpServers = [
            'legendary-design.js',
            'legendary-ai-chat.js',
            'legendary-image-gen.js',
            'legendary-search.js',
            'legendary-video.js'
        ];

        mcpServers.forEach(script => {
            const scriptPath = path.join(this.systemPath, 'scripts', script);
            if (fs.existsSync(scriptPath)) {
                console.log(`  ✅ ${script}`);
                this.results.push({ test: `MCP Script: ${script}`, status: 'PASS' });
            } else {
                console.log(`  ❌ ${script}`);
                this.results.push({ test: `MCP Script: ${script}`, status: 'FAIL' });
            }
        });
    }

    generateReport() {
        console.log('\n📊 Test Results Summary:');
        console.log('=' .repeat(50));
        
        const passed = this.results.filter(r => r.status === 'PASS').length;
        const failed = this.results.filter(r => r.status === 'FAIL').length;
        const total = this.results.length;
        
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
        
        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`  - ${result.test}`);
            });
        }
        
        // Create system status
        const status = {
            testDate: new Date().toISOString(),
            totalTests: total,
            passed: passed,
            failed: failed,
            successRate: Math.round((passed / total) * 100),
            systemReady: failed === 0,
            results: this.results
        };
        
        const statusFile = path.join(this.systemPath, 'test-results.json');
        fs.writeFileSync(statusFile, JSON.stringify(status, null, 2));
        
        console.log('\n🎯 Next Steps:');
        if (failed === 0) {
            console.log('✅ System is ready! Restart Claude Desktop to activate MCP servers.');
            console.log('🎨 Test with: "Create a professional book cover design"');
        } else {
            console.log('❌ Please fix the failed components before using the system.');
        }
        
        console.log(`\n📁 Test results saved to: ${statusFile}`);
    }
}

// Run tests if called directly
if (require.main === module) {
    const test = new SystemTest();
    test.runTests();
}

module.exports = SystemTest;
