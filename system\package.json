{"name": "augment-legendary-system", "version": "1.0.0", "description": "Augment-Legendary MCP System with advanced design and AI capabilities", "main": "augment-bridge.js", "scripts": {"start": "node augment-bridge.js", "install-mcps": "npm install -g @modelcontextprotocol/server-filesystem @modelcontextprotocol/server-brave-search", "setup-system": "node setup-system.js", "test-bridge": "node test-bridge.js"}, "keywords": ["augment", "mcp", "design", "ai", "legendary"], "author": "Teacher <PERSON><PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "fs-extra": "^11.2.0", "sharp": "^0.33.0", "canvas": "^2.11.2", "jimp": "^0.22.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "local"}}